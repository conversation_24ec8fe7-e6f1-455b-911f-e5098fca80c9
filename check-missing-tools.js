// Script to check for missing tools between toolCategories and tool-registry

const fs = require('fs');
const path = require('path');

// Read toolCategories from tools.ts
const toolsFile = fs.readFileSync(path.join(__dirname, 'src/lib/tools.ts'), 'utf8');

// Extract tool slugs from toolCategories
const toolCategoriesMatch = toolsFile.match(/export const toolCategories: ToolCategory\[\] = \[([\s\S]*?)\];/);
if (!toolCategoriesMatch) {
  console.error('Could not find toolCategories in tools.ts');
  process.exit(1);
}

const toolCategoriesContent = toolCategoriesMatch[1];
const slugMatches = toolCategoriesContent.match(/slug: ['"`]([^'"`]+)['"`]/g);

if (!slugMatches) {
  console.error('Could not find tool slugs in toolCategories');
  process.exit(1);
}

const toolCategoriesSlugs = slugMatches
  .map(match => match.match(/slug: ['"`]([^'"`]+)['"`]/)[1])
  .filter(slug => slug !== 'financial-calculators' && slug !== 'payroll-tools' && slug !== 'converters' && slug !== 'health-calculators' && slug !== 'date-tools' && slug !== 'education-tools' && slug !== 'text-tools' && slug !== 'general-calculators' && slug !== 'personality-tests' && slug !== 'tech-tools' && slug !== 'islamic-tools' && slug !== 'fun-tools' && slug !== 'pdf-tools' && slug !== 'image-tools'); // Filter out category slugs

// Read tool-registry.ts
const registryFile = fs.readFileSync(path.join(__dirname, 'src/lib/tool-registry.ts'), 'utf8');

// Extract tool slugs from toolComponentMap
const toolComponentMapMatch = registryFile.match(/const toolComponentMap: \{ \[key: string\]: ComponentType<any> \} = \{([\s\S]*?)\};/);
if (!toolComponentMapMatch) {
  console.error('Could not find toolComponentMap in tool-registry.ts');
  process.exit(1);
}

const toolComponentMapContent = toolComponentMapMatch[1];
const registrySlugMatches = toolComponentMapContent.match(/['"`]([^'"`]+)['"`]:/g);

if (!registrySlugMatches) {
  console.error('Could not find tool slugs in toolComponentMap');
  process.exit(1);
}

const registrySlugs = registrySlugMatches.map(match => match.match(/['"`]([^'"`]+)['"`]:/)[1]);

// Find missing tools
const missingInRegistry = toolCategoriesSlugs.filter(slug => !registrySlugs.includes(slug));
const missingInCategories = registrySlugs.filter(slug => !toolCategoriesSlugs.includes(slug));

console.log('=== Tool Categories Slugs ===');
console.log(toolCategoriesSlugs.sort());
console.log('\n=== Registry Slugs ===');
console.log(registrySlugs.sort());

console.log('\n=== Missing in Registry (in toolCategories but not in tool-registry) ===');
if (missingInRegistry.length > 0) {
  missingInRegistry.forEach(slug => console.log(`- ${slug}`));
} else {
  console.log('None');
}

console.log('\n=== Missing in Categories (in tool-registry but not in toolCategories) ===');
if (missingInCategories.length > 0) {
  missingInCategories.forEach(slug => console.log(`- ${slug}`));
} else {
  console.log('None');
}

console.log(`\nTotal tools in categories: ${toolCategoriesSlugs.length}`);
console.log(`Total tools in registry: ${registrySlugs.length}`);
